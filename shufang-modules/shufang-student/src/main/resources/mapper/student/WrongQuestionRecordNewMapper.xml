<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jxw.shufang.student.mapper.WrongQuestionRecordNewMapper">

    <sql id="BaseColumns">
        wrong_question_record_new_id, student_id, question_id, subject_id, user_answer, user_answer_status, source_type, source_id, create_dept, create_by, create_time, update_by, update_time
    </sql>

    <select id="selectWrongQuestionRecordNewPage"
            resultType="com.jxw.shufang.student.domain.vo.WrongQuestionRecordNewVo">
        select <include refid="BaseColumns"/>
        from wrong_question_record_new
        <where>
            <if test="ew.isNonEmptyOfWhere()">
                and ${ew.sqlSegment}
            </if>
        </where>
    </select>
</mapper>
