package com.jxw.shufang.student.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jxw.shufang.common.core.utils.MapstructUtils;
import com.jxw.shufang.common.core.utils.StringUtils;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.web.core.BaseService;
import com.jxw.shufang.extresource.api.RemoteQuestionService;
import com.jxw.shufang.extresource.api.domain.vo.RemoteQuestionVo;
import com.jxw.shufang.student.domain.WrongQuestionRecordNew;
import com.jxw.shufang.student.domain.bo.WrongQuestionRecordNewBo;
import com.jxw.shufang.student.domain.vo.CourseVo;
import com.jxw.shufang.student.domain.vo.WrongQuestionRecordNewVo;
import com.jxw.shufang.student.mapper.WrongQuestionRecordNewMapper;
import com.jxw.shufang.student.service.ICourseService;
import com.jxw.shufang.student.service.IWrongQuestionRecordNewService;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Service
public class WrongQuestionRecordNewServiceImpl implements IWrongQuestionRecordNewService, BaseService {
    private final WrongQuestionRecordNewMapper baseMapper;
    private final ICourseService courseService;
    @DubboReference
    private RemoteQuestionService remoteQuestionService;

    @Override
    public TableDataInfo<WrongQuestionRecordNewVo> pageWrongQuestionRecord(WrongQuestionRecordNewBo wrongQuestionRecordNewBo, PageQuery pageQuery) {
        LambdaQueryWrapper<WrongQuestionRecordNew> lqw = buildLambdaQueryWrapper(wrongQuestionRecordNewBo);
        Page<WrongQuestionRecordNewVo> result = baseMapper.selectWrongQuestionRecordNewPage(pageQuery.build(), lqw);
        List<WrongQuestionRecordNewVo> wrongQuestions = result.getRecords();
        List<Long> questionIds = wrongQuestions.stream().map(WrongQuestionRecordNewVo::getQuestionId).collect(Collectors.toList());
        Map<Long, RemoteQuestionVo> questionIdMap = getQuestionIdMapWithParent(questionIds);

        putTopCourseInfo(wrongQuestions, true);
        wrongQuestions.forEach(question -> {
            RemoteQuestionVo remoteQuestionVo = questionIdMap.get(question.getQuestionId());
            Long pid = remoteQuestionVo.getPid();
            question.setQuestion(remoteQuestionVo);
            if (ObjectUtils.isEmpty(pid) || pid == 0) {
                return;
            }
            RemoteQuestionVo parentQuestion = questionIdMap.get(pid);
            //由于操作的是同一个父级，需要重新生成每一个父级Question
            remoteQuestionVo.setParentQuestion(parentQuestion);
        });
        return TableDataInfo.build(result);
    }

    private void putTopCourseInfo(List<WrongQuestionRecordNewVo> wrongQuestionRecords, boolean queryDetailInfo) {
        if (CollUtil.isEmpty(wrongQuestionRecords)) {
            return;
        }
        List<Long> courseIds = wrongQuestionRecords.stream().map(WrongQuestionRecordNewVo::getSourceId).map(Long::valueOf).toList();
        if (CollUtil.isEmpty(courseIds)) {
            return;
        }
        List<CourseVo> courseVos = courseService.listCourseVos(courseIds);
        courseService.putTopmostCourseInfo(courseVos, queryDetailInfo);
        if (queryDetailInfo) {
            List<CourseVo> list = courseVos.stream().map(CourseVo::getTopmostCourse).toList();
            courseService.putCourseDetail(list, true);
        }
        Map<Long, CourseVo> courseVoMap = courseVos.stream().collect(Collectors.toMap(CourseVo::getCourseId, Function.identity()));
        wrongQuestionRecords.forEach(wrongQuestionRecord -> {
            wrongQuestionRecord.setSource(courseVoMap.get(Long.valueOf(wrongQuestionRecord.getSourceId())));
        });
    }

    private LambdaQueryWrapper<WrongQuestionRecordNew> buildLambdaQueryWrapper(WrongQuestionRecordNewBo bo) {
        LambdaQueryWrapper<WrongQuestionRecordNew> lqw = Wrappers.lambdaQuery();
        lqw.eq(WrongQuestionRecordNew::getStudentId, bo.getStudentId());
        lqw.eq(bo.getSubjectId() != null, WrongQuestionRecordNew::getSubjectId, bo.getSubjectId());
        lqw.eq(StringUtils.isNotBlank(bo.getSourceType()), WrongQuestionRecordNew::getSourceType, bo.getSourceType());
        lqw.orderByDesc(WrongQuestionRecordNew::getUpdateTime);
        return lqw;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertBatchByBo(List<WrongQuestionRecordNewBo> bos) {
        //TODO 校验sourceType类型
        List<WrongQuestionRecordNew> wrongQuestionRecordNews = MapstructUtils.convert(bos, WrongQuestionRecordNew.class);
        if (CollUtil.isEmpty(wrongQuestionRecordNews)) {
            return true;
        }
        List<Long> questionIds = wrongQuestionRecordNews.stream().map(WrongQuestionRecordNew::getQuestionId).toList();

        Map<Long, RemoteQuestionVo> questionIdMap = getQuestionIdMap(questionIds);

        for (WrongQuestionRecordNew wrongQuestion : wrongQuestionRecordNews) {
            wrongQuestion.setSubjectId(questionIdMap.get(wrongQuestion.getQuestionId()).getSubjectId().intValue());
        }

        LambdaQueryWrapper<WrongQuestionRecordNew> lqw = buildRecordslqw(wrongQuestionRecordNews);
        List<WrongQuestionRecordNew> existsWrongQuestionRecord = baseMapper.selectList(lqw);

        Map<String, Long> existRecordIdsMap = existsWrongQuestionRecord.stream()
            .collect(Collectors.toMap(existRecord -> existRecord.getQuestionId() + "-" + existRecord.getSourceId() + "-" + existRecord.getSourceType() + "-" + existRecord.getStudentId(), WrongQuestionRecordNew::getWrongQuestionRecordNewId));

        for (WrongQuestionRecordNew wrongQuestion : wrongQuestionRecordNews) {
            String key = wrongQuestion.getQuestionId() + "-" + wrongQuestion.getSourceId() + "-" + wrongQuestion.getSourceType() + "-" + wrongQuestion.getStudentId();
            Long existingRecordId = existRecordIdsMap.get(key);
            if (existingRecordId != null) {
                wrongQuestion.setWrongQuestionRecordNewId(existingRecordId);
            }
        }
        return baseMapper.insertOrUpdateBatch(wrongQuestionRecordNews);
    }

    /**
     * 批量删除错题记录
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    private LambdaQueryWrapper<WrongQuestionRecordNew> buildRecordslqw(List<WrongQuestionRecordNew> wrongQuestionRecordNews) {
        LambdaQueryWrapper<WrongQuestionRecordNew> lqw = Wrappers.lambdaQuery();
        lqw.in(WrongQuestionRecordNew::getQuestionId, wrongQuestionRecordNews.stream().map(WrongQuestionRecordNew::getQuestionId).toList());
        lqw.eq(WrongQuestionRecordNew::getStudentId, wrongQuestionRecordNews.get(0).getStudentId());
        lqw.eq(WrongQuestionRecordNew::getSourceId, wrongQuestionRecordNews.get(0).getSourceId());
        lqw.eq(WrongQuestionRecordNew::getSourceType, wrongQuestionRecordNews.get(0).getSourceType());
        return lqw;
    }

    private Map<Long, RemoteQuestionVo> getQuestionIdMap(List<Long> questionIds) {
        List<RemoteQuestionVo> remoteQuestionVos = remoteQuestionService.listQuestions(questionIds);
        return remoteQuestionVos.stream().collect(Collectors.toMap(RemoteQuestionVo::getId, Function.identity()));
    }

    private Map<Long, RemoteQuestionVo> getQuestionIdMapWithParent(List<Long> questionIds) {
        List<RemoteQuestionVo> remoteQuestionVos = remoteQuestionService.listQuestions(questionIds);
        if (CollUtil.isEmpty(remoteQuestionVos)){
            return new HashMap<>();
        }
        List<Long> list = remoteQuestionVos.stream().map(RemoteQuestionVo::getPid).filter(pid -> !(ObjectUtils.isEmpty(pid) || pid == 0)).toList();
        if (CollUtil.isNotEmpty(list)){
            List<RemoteQuestionVo> parentQuestionList = remoteQuestionService.listQuestions(list);
            if (CollUtil.isNotEmpty(parentQuestionList)){
                remoteQuestionVos.addAll(parentQuestionList);
            }
        }
        return remoteQuestionVos.stream().collect(Collectors.toMap(RemoteQuestionVo::getId, Function.identity()));
    }
}
