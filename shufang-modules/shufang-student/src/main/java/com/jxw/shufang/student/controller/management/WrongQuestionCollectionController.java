package com.jxw.shufang.student.controller.management;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.excel.utils.ExcelUtil;
import com.jxw.shufang.common.idempotent.annotation.RepeatSubmit;
import com.jxw.shufang.common.log.annotation.Log;
import com.jxw.shufang.common.log.enums.BusinessType;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.web.core.BaseController;
import com.jxw.shufang.student.domain.bo.WrongQuestionCollectionBo;
import com.jxw.shufang.student.domain.vo.WrongQuestionCollectionVo;
import com.jxw.shufang.student.service.IWrongQuestionCollectionService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 错题合集
 * 前端访问路由地址为:/student/wrongQuestionCollection
 *
 * <AUTHOR>
 * @date 2024-07-30
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/management/wrongQuestionCollection")
public class WrongQuestionCollectionController extends BaseController {

    private final IWrongQuestionCollectionService wrongQuestionCollectionService;

    /**
     * 查询错题合集列表
     */
    @SaCheckPermission("student:wrongQuestionCollection:list")
    @GetMapping("/list")
    public TableDataInfo<WrongQuestionCollectionVo> list(WrongQuestionCollectionBo bo, PageQuery pageQuery) {
        return wrongQuestionCollectionService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出错题合集列表
     */
    @SaCheckPermission("student:wrongQuestionCollection:export")
    @Log(title = "错题合集", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(WrongQuestionCollectionBo bo, HttpServletResponse response) {
        List<WrongQuestionCollectionVo> list = wrongQuestionCollectionService.queryList(bo);
        ExcelUtil.exportExcel(list, "错题合集", WrongQuestionCollectionVo.class, response);
    }

    /**
     * 获取错题合集详细信息
     *
     * @param wrongQuestionCollectionId 主键
     */
    @SaCheckPermission("student:wrongQuestionCollection:query")
    @GetMapping("/{wrongQuestionCollectionId}")
    public R<WrongQuestionCollectionVo> getInfo(@NotNull(message = "主键不能为空")
                                                 @PathVariable Long wrongQuestionCollectionId) {
        return R.ok(wrongQuestionCollectionService.queryById(wrongQuestionCollectionId));
    }

    /**
     * 新增错题合集
     */
    @SaCheckPermission("student:wrongQuestionCollection:add")
    @Log(title = "错题合集", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody WrongQuestionCollectionBo bo) {
        return toAjax(wrongQuestionCollectionService.insertByBo(bo));
    }

    /**
     * 修改错题合集
     */
    @SaCheckPermission("student:wrongQuestionCollection:edit")
    @Log(title = "错题合集", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody WrongQuestionCollectionBo bo) {
        return toAjax(wrongQuestionCollectionService.updateByBo(bo));
    }

    /**
     * 删除错题合集
     *
     * @param wrongQuestionCollectionIds 主键串
     */
    @SaCheckPermission("student:wrongQuestionCollection:remove")
    @Log(title = "错题合集", businessType = BusinessType.DELETE)
    @DeleteMapping("/{wrongQuestionCollectionIds}")
    public R<Void> remove(@NotNull(message = "主键不能为空")
                          @PathVariable Long[] wrongQuestionCollectionIds) {
        return toAjax(wrongQuestionCollectionService.deleteWithValidByIds(List.of(wrongQuestionCollectionIds), true));
    }

    /**
     * 根据学生ID查询错题合集列表
     */
    @SaCheckPermission("student:wrongQuestionCollection:query")
    @GetMapping("/student/{studentId}")
    public R<List<WrongQuestionCollectionVo>> getByStudentId(@NotNull(message = "学生ID不能为空")
                                                              @PathVariable Long studentId) {
        return R.ok(wrongQuestionCollectionService.queryByStudentId(studentId));
    }

    /**
     * 根据学生ID和类型查询错题合集列表
     */
    @SaCheckPermission("student:wrongQuestionCollection:query")
    @GetMapping("/student/{studentId}/type/{collectionType}")
    public R<List<WrongQuestionCollectionVo>> getByStudentIdAndType(@NotNull(message = "学生ID不能为空")
                                                                     @PathVariable Long studentId,
                                                                     @NotNull(message = "合集类型不能为空")
                                                                     @PathVariable Integer collectionType) {
        return R.ok(wrongQuestionCollectionService.queryByStudentIdAndType(studentId, collectionType));
    }

    /**
     * 更新错题合集状态
     */
    @SaCheckPermission("student:wrongQuestionCollection:edit")
    @Log(title = "错题合集", businessType = BusinessType.UPDATE)
    @PutMapping("/{wrongQuestionCollectionId}/status/{collectionStatus}")
    public R<Void> updateStatus(@NotNull(message = "错题合集ID不能为空")
                                @PathVariable Long wrongQuestionCollectionId,
                                @NotNull(message = "合集状态不能为空")
                                @PathVariable Integer collectionStatus) {
        return toAjax(wrongQuestionCollectionService.updateCollectionStatus(wrongQuestionCollectionId, collectionStatus));
    }

}
