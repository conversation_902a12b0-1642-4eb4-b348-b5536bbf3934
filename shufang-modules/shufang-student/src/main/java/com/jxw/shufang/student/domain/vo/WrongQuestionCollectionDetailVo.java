package com.jxw.shufang.student.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.jxw.shufang.common.excel.annotation.ExcelDictFormat;
import com.jxw.shufang.common.excel.convert.ExcelDictConvert;
import com.jxw.shufang.student.domain.WrongQuestionCollectionDetail;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 错题合集详情视图对象 wrong_question_collection_detail
 *
 * <AUTHOR>
 * @date 2024-07-30
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = WrongQuestionCollectionDetail.class)
public class WrongQuestionCollectionDetailVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 错题合集详情id
     */
    @ExcelProperty(value = "错题合集详情id")
    private Long wrongQuestionCollectionDetailId;

    /**
     * 错题合集id
     */
    @ExcelProperty(value = "错题合集id")
    private Long wrongQuestionCollectionId;

    /**
     * 问题id
     */
    @ExcelProperty(value = "问题id")
    private Long questionId;

    /**
     * 题目序号
     */
    @ExcelProperty(value = "题目序号")
    private String questionNo;

    /**
     * 作答结果（对应字典值，如全错 半错）
     */
    @ExcelProperty(value = "作答结果", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "answer_result")
    private String answerResult;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @ExcelProperty(value = "更新时间")
    private Date updateTime;

}
