package com.jxw.shufang.student.controller.management;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.jxw.shufang.student.domain.vo.WrongQuestionRecordV2Vo;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.excel.utils.ExcelUtil;
import com.jxw.shufang.common.idempotent.annotation.RepeatSubmit;
import com.jxw.shufang.common.log.annotation.Log;
import com.jxw.shufang.common.log.enums.BusinessType;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.web.core.BaseController;
import com.jxw.shufang.student.domain.bo.WrongQuestionRecordBo;
import com.jxw.shufang.student.domain.vo.WrongQuestionGroupVo;
import com.jxw.shufang.student.domain.vo.WrongQuestionRecordVo;
import com.jxw.shufang.student.service.IWrongQuestionRecordService;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 错题记录
 * 前端访问路由地址为:/student/wrongQuestionRecord
 *
 *
 * @date 2024-05-09
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/management/wrongQuestionRecord")
public class WrongQuestionRecordController extends BaseController {

    private final IWrongQuestionRecordService wrongQuestionRecordService;

    /**
     * 查询错题记录列表
     */
    @SaCheckPermission("student:wrongQuestionRecord:list")
    @GetMapping("/list")
    public TableDataInfo<WrongQuestionRecordVo> list(WrongQuestionRecordBo bo, PageQuery pageQuery) {
        return wrongQuestionRecordService.selectWrongQuestionRecordPage(bo, pageQuery);
    }

    /**
     * 查询错题记录列表
     */
    @SaCheckPermission("student:wrongQuestionRecord:list")
    @GetMapping("/v2/list")
    public TableDataInfo<WrongQuestionRecordV2Vo> listV2(WrongQuestionRecordBo bo, PageQuery pageQuery) {
        return wrongQuestionRecordService.selectWrongQuestionRecordPageV2(bo, pageQuery);
    }


    /**
     * 查询错题记录列表
     */
    @SaCheckPermission("student:wrongQuestionRecord:list")
    @PostMapping("/v2/query")
    public TableDataInfo<WrongQuestionRecordV2Vo> queryV2(@RequestBody WrongQuestionRecordBo bo, PageQuery pageQuery) {
        return wrongQuestionRecordService.selectWrongQuestionRecordPageV2(bo, pageQuery);
    }

    /**
     * 分页按照日期分组查询错题列表（不会查总数），使用滑动到底加载更多的方式
     *
     */
    @SaCheckPermission("student:wrongQuestionRecord:list")
    @GetMapping("/listGroupByDate")
    public TableDataInfo<WrongQuestionGroupVo> listGroupByDate(@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") String recordCreateTimeStart,
                                                               @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") String recordCreateTimeEnd,
                                                               Long studentId, String affiliationSubject, PageQuery pageQuery) {
        return wrongQuestionRecordService.listGroupByDateV2(recordCreateTimeStart,recordCreateTimeEnd,studentId,affiliationSubject,pageQuery);
    }

    /**
     * 导出错题记录列表
     */
    @SaCheckPermission("student:wrongQuestionRecord:export")
    @Log(title = "错题记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(WrongQuestionRecordBo bo, HttpServletResponse response) {
        List<WrongQuestionRecordVo> list = wrongQuestionRecordService.queryList(bo);
        ExcelUtil.exportExcel(list, "错题记录", WrongQuestionRecordVo.class, response);
    }

    /**
     * 获取错题记录详细信息
     *
     * @param wrongQuestionRecordId 主键
     */
    @SaCheckPermission("student:wrongQuestionRecord:query")
    @GetMapping("/{wrongQuestionRecordId}")
    public R<WrongQuestionRecordVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long wrongQuestionRecordId) {
        return R.ok(wrongQuestionRecordService.queryById(wrongQuestionRecordId));
    }

    /**
     * 获取错题记录详细信息
     *
     * @param wrongQuestionRecordId 主键
     */
    @SaCheckPermission("student:wrongQuestionRecord:query")
    @GetMapping("/v2/{wrongQuestionRecordId}")
    public R<WrongQuestionRecordVo> getInfoV2(@NotNull(message = "主键不能为空")
                                            @PathVariable Long wrongQuestionRecordId) {
        WrongQuestionRecordVo wrongQuestionRecordVo = wrongQuestionRecordService.queryByIdV2(wrongQuestionRecordId);
        return R.ok(wrongQuestionRecordVo);
    }

    /**
     * 新增错题记录
     */
    @SaCheckPermission("student:wrongQuestionRecord:add")
    @Log(title = "错题记录", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody WrongQuestionRecordBo bo) {
        return toAjax(wrongQuestionRecordService.insertByBo(bo));
    }

    /**
     * 修改错题记录
     */
    @SaCheckPermission("student:wrongQuestionRecord:edit")
    @Log(title = "错题记录", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody WrongQuestionRecordBo bo) {
        return toAjax(wrongQuestionRecordService.updateByBo(bo));
    }

    /**
     * 删除错题记录
     *
     * @param wrongQuestionRecordIds 主键串
     */
    @SaCheckPermission("student:wrongQuestionRecord:remove")
    @Log(title = "错题记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{wrongQuestionRecordIds}")
    public R<Void> remove(@NotNull(message = "主键不能为空")
                          @PathVariable Long[] wrongQuestionRecordIds) {
        return toAjax(wrongQuestionRecordService.deleteWithValidByIds(List.of(wrongQuestionRecordIds), true));
    }

    /**
     * 关联子题
     */
    @PostMapping("/mergeSubQuestionsByRelation")
    public R<List<WrongQuestionRecordV2Vo>> mergeSubQuestionsByRelation(@RequestBody WrongQuestionRecordBo bo, PageQuery pageQuery,@RequestParam(defaultValue = "false") Boolean isRelevancy) {
        return R.ok(wrongQuestionRecordService.mergeSubQuestionsByRelation(bo, pageQuery,isRelevancy));
    }

}
