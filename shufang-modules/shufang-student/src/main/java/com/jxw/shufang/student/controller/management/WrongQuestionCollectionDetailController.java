package com.jxw.shufang.student.controller.management;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.excel.utils.ExcelUtil;
import com.jxw.shufang.common.idempotent.annotation.RepeatSubmit;
import com.jxw.shufang.common.log.annotation.Log;
import com.jxw.shufang.common.log.enums.BusinessType;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.web.core.BaseController;
import com.jxw.shufang.student.domain.bo.WrongQuestionCollectionDetailBo;
import com.jxw.shufang.student.domain.vo.WrongQuestionCollectionDetailVo;
import com.jxw.shufang.student.service.IWrongQuestionCollectionDetailService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 错题合集详情
 * 前端访问路由地址为:/student/wrongQuestionCollectionDetail
 *
 * <AUTHOR>
 * @date 2024-07-30
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/management/wrongQuestionCollectionDetail")
public class WrongQuestionCollectionDetailController extends BaseController {

    private final IWrongQuestionCollectionDetailService wrongQuestionCollectionDetailService;

    /**
     * 查询错题合集详情列表
     */
    @SaCheckPermission("student:wrongQuestionCollectionDetail:list")
    @GetMapping("/list")
    public TableDataInfo<WrongQuestionCollectionDetailVo> list(WrongQuestionCollectionDetailBo bo, PageQuery pageQuery) {
        return wrongQuestionCollectionDetailService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出错题合集详情列表
     */
    @SaCheckPermission("student:wrongQuestionCollectionDetail:export")
    @Log(title = "错题合集详情", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(WrongQuestionCollectionDetailBo bo, HttpServletResponse response) {
        List<WrongQuestionCollectionDetailVo> list = wrongQuestionCollectionDetailService.queryList(bo);
        ExcelUtil.exportExcel(list, "错题合集详情", WrongQuestionCollectionDetailVo.class, response);
    }

    /**
     * 获取错题合集详情详细信息
     *
     * @param wrongQuestionCollectionDetailId 主键
     */
    @SaCheckPermission("student:wrongQuestionCollectionDetail:query")
    @GetMapping("/{wrongQuestionCollectionDetailId}")
    public R<WrongQuestionCollectionDetailVo> getInfo(@NotNull(message = "主键不能为空")
                                                       @PathVariable Long wrongQuestionCollectionDetailId) {
        return R.ok(wrongQuestionCollectionDetailService.queryById(wrongQuestionCollectionDetailId));
    }

    /**
     * 新增错题合集详情
     */
    @SaCheckPermission("student:wrongQuestionCollectionDetail:add")
    @Log(title = "错题合集详情", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody WrongQuestionCollectionDetailBo bo) {
        return toAjax(wrongQuestionCollectionDetailService.insertByBo(bo));
    }

    /**
     * 修改错题合集详情
     */
    @SaCheckPermission("student:wrongQuestionCollectionDetail:edit")
    @Log(title = "错题合集详情", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody WrongQuestionCollectionDetailBo bo) {
        return toAjax(wrongQuestionCollectionDetailService.updateByBo(bo));
    }

    /**
     * 删除错题合集详情
     *
     * @param wrongQuestionCollectionDetailIds 主键串
     */
    @SaCheckPermission("student:wrongQuestionCollectionDetail:remove")
    @Log(title = "错题合集详情", businessType = BusinessType.DELETE)
    @DeleteMapping("/{wrongQuestionCollectionDetailIds}")
    public R<Void> remove(@NotNull(message = "主键不能为空")
                          @PathVariable Long[] wrongQuestionCollectionDetailIds) {
        return toAjax(wrongQuestionCollectionDetailService.deleteWithValidByIds(List.of(wrongQuestionCollectionDetailIds), true));
    }

    /**
     * 根据错题合集ID查询详情列表
     */
    @SaCheckPermission("student:wrongQuestionCollectionDetail:query")
    @GetMapping("/collection/{wrongQuestionCollectionId}")
    public R<List<WrongQuestionCollectionDetailVo>> getByCollectionId(@NotNull(message = "错题合集ID不能为空")
                                                                       @PathVariable Long wrongQuestionCollectionId) {
        return R.ok(wrongQuestionCollectionDetailService.queryByCollectionId(wrongQuestionCollectionId));
    }

    /**
     * 批量新增错题合集详情
     */
    @SaCheckPermission("student:wrongQuestionCollectionDetail:add")
    @Log(title = "错题合集详情", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping("/batch")
    public R<Void> addBatch(@Validated(AddGroup.class) @RequestBody List<WrongQuestionCollectionDetailBo> boList) {
        return toAjax(wrongQuestionCollectionDetailService.insertBatchByBo(boList));
    }

}
