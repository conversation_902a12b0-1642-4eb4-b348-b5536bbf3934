package com.jxw.shufang.student.service;

import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.student.domain.bo.WrongQuestionRecordBo;
import com.jxw.shufang.student.domain.vo.WrongQuestionGroupVo;
import com.jxw.shufang.student.domain.vo.WrongQuestionRecordV2Vo;
import com.jxw.shufang.student.domain.vo.WrongQuestionRecordVo;

import java.util.Collection;
import java.util.List;

/**
 * 错题记录Service接口
 *
 *
 * @date 2024-05-09
 */
public interface IWrongQuestionRecordService {

    /**
     * 查询错题记录
     */
    WrongQuestionRecordVo queryById(Long wrongQuestionRecordId);

    /**
     * 查询错题记录列表
     */
    TableDataInfo<WrongQuestionRecordVo> selectWrongQuestionRecordPage(WrongQuestionRecordBo bo, PageQuery pageQuery);

    /**
     * 查询错题记录列表
     */
    List<WrongQuestionRecordVo> queryList(WrongQuestionRecordBo bo);

    /**
     * 新增错题记录
     */
    Boolean insertByBo(WrongQuestionRecordBo bo);

    /**
     * 修改错题记录
     */
    Boolean updateByBo(WrongQuestionRecordBo bo);

    /**
     * 校验并批量删除错题记录信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    Boolean insertBatchByBo(List<WrongQuestionRecordBo> collect);

    TableDataInfo<WrongQuestionGroupVo> listGroupByDate(Long studentId,String affiliationSubject, PageQuery pageQuery);
    TableDataInfo<WrongQuestionGroupVo> listGroupByDateV2(String recordCreateTimeStart, String recordCreateTimeEnd,
                                                          Long studentId, String affiliationSubject, PageQuery pageQuery);

    TableDataInfo<WrongQuestionRecordV2Vo> selectWrongQuestionRecordPageV2(WrongQuestionRecordBo bo, PageQuery pageQuery);

    WrongQuestionRecordVo queryByIdV2(Long wrongQuestionRecordId);

    List<WrongQuestionRecordV2Vo> mergeSubQuestionsByRelation(WrongQuestionRecordBo bo, PageQuery pageQuery, Boolean isRelevancy);
}
