package com.jxw.shufang.student.domain.bo;

import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;
import com.jxw.shufang.student.domain.WrongQuestionCollection;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 错题合集业务对象 wrong_question_collection
 *
 * <AUTHOR>
 * @date 2024-07-30
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = WrongQuestionCollection.class, reverseConvertGenerate = false)
public class WrongQuestionCollectionBo extends BaseEntity {

    /**
     * 错题合集id
     */
    @NotNull(message = "错题合集id不能为空", groups = { EditGroup.class })
    private Long wrongQuestionCollectionId;

    /**
     * 会员id
     */
    @NotNull(message = "会员id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long studentId;

    /**
     * 错题合集状态 0-未订正 1-已订正
     */
    private Integer collectionStatus;

    /**
     * 错题合集类型 1-打印 2-上周错题汇总
     */
    @NotNull(message = "错题合集类型不能为空", groups = { AddGroup.class, EditGroup.class })
    private Integer collectionType;

    /**
     * 订正截图（oss_id，多个，逗号隔开）
     */
    private String reviseScreenshots;

    /**
     * 错题评语
     */
    private String remark;

}
