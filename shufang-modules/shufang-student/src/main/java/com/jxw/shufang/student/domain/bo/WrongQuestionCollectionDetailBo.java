package com.jxw.shufang.student.domain.bo;

import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;
import com.jxw.shufang.student.domain.WrongQuestionCollectionDetail;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 错题合集详情业务对象 wrong_question_collection_detail
 *
 * <AUTHOR>
 * @date 2024-07-30
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = WrongQuestionCollectionDetail.class, reverseConvertGenerate = false)
public class WrongQuestionCollectionDetailBo extends BaseEntity {

    /**
     * 错题合集详情id
     */
    @NotNull(message = "错题合集详情id不能为空", groups = { EditGroup.class })
    private Long wrongQuestionCollectionDetailId;

    /**
     * 错题合集id
     */
    @NotNull(message = "错题合集id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long wrongQuestionCollectionId;

    /**
     * 问题id
     */
    private Long questionId;

    /**
     * 题目序号
     */
    private String questionNo;

    /**
     * 作答结果（对应字典值，如全错 半错）
     */
    private String answerResult;

}
