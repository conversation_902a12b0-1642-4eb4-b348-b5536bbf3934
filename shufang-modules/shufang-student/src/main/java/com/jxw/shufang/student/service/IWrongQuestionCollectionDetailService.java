package com.jxw.shufang.student.service;

import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.student.domain.bo.WrongQuestionCollectionDetailBo;
import com.jxw.shufang.student.domain.vo.WrongQuestionCollectionDetailVo;

import java.util.Collection;
import java.util.List;

/**
 * 错题合集详情Service接口
 *
 * <AUTHOR>
 * @date 2024-07-30
 */
public interface IWrongQuestionCollectionDetailService {

    /**
     * 查询错题合集详情
     */
    WrongQuestionCollectionDetailVo queryById(Long wrongQuestionCollectionDetailId);

    /**
     * 查询错题合集详情列表
     */
    TableDataInfo<WrongQuestionCollectionDetailVo> queryPageList(WrongQuestionCollectionDetailBo bo, PageQuery pageQuery);

    /**
     * 查询错题合集详情列表
     */
    List<WrongQuestionCollectionDetailVo> queryList(WrongQuestionCollectionDetailBo bo);

    /**
     * 新增错题合集详情
     */
    Boolean insertByBo(WrongQuestionCollectionDetailBo bo);

    /**
     * 修改错题合集详情
     */
    Boolean updateByBo(WrongQuestionCollectionDetailBo bo);

    /**
     * 校验并批量删除错题合集详情信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 根据错题合集ID查询详情列表
     */
    List<WrongQuestionCollectionDetailVo> queryByCollectionId(Long wrongQuestionCollectionId);

    /**
     * 批量新增错题合集详情
     */
    Boolean insertBatchByBo(List<WrongQuestionCollectionDetailBo> boList);

    /**
     * 根据错题合集ID删除详情
     */
    Boolean deleteByCollectionId(Long wrongQuestionCollectionId);

}
