package com.jxw.shufang.student.controller.android;

import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.core.exception.ServiceException;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.idempotent.annotation.RepeatSubmit;
import com.jxw.shufang.common.log.annotation.Log;
import com.jxw.shufang.common.log.enums.BusinessType;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.satoken.utils.LoginHelper;
import com.jxw.shufang.common.web.core.BaseController;
import com.jxw.shufang.extresource.api.RemoteCdsCommonService;
import com.jxw.shufang.extresource.api.domain.vo.RemoteSubjectVo;
import com.jxw.shufang.student.domain.bo.WrongQuestionRecordNewBo;
import com.jxw.shufang.student.domain.vo.WrongQuestionRecordNewVo;
import com.jxw.shufang.student.service.IWrongQuestionRecordNewService;
import com.jxw.shufang.system.api.RemoteDictService;
import com.jxw.shufang.system.api.domain.vo.RemoteDictDataVo;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/android/wrongQuestionRecordNew")
public class AWrongQuestionRecordNewController extends BaseController {

    private final IWrongQuestionRecordNewService recordService;
    private final RemoteCdsCommonService remoteCdsCommonService;
    private final RemoteDictService remoteDictService;

    @GetMapping("/page")
    public TableDataInfo<WrongQuestionRecordNewVo> pageWrongQuestionRecord(WrongQuestionRecordNewBo wrongQuestionRecordNewBo,
                                                                           PageQuery pageQuery) {
        if (!LoginHelper.isStudent()) {
            throw new ServiceException("仅允许会员可以访问");
        }
        pageQuery.setSearchCount(false);
        wrongQuestionRecordNewBo.setStudentId(LoginHelper.getStudentId());
        return recordService.pageWrongQuestionRecord(wrongQuestionRecordNewBo, pageQuery);
    }

    @PostMapping()
    @RepeatSubmit
    public R<Void> insertBatchByBo(@Validated(AddGroup.class) @RequestBody List<WrongQuestionRecordNewBo> bos) {
        if (!LoginHelper.isStudent()) {
            throw new ServiceException("仅允许会员可以访问");
        }
        bos.forEach(bo -> bo.setStudentId(LoginHelper.getStudentId()));
        return toAjax(recordService.insertBatchByBo(bos));
    }

    /**
     * 删除错题记录
     *
     * @param wrongQuestionRecordNewId 主键
     */
    @RepeatSubmit
    @Log(title = "错题记录（新）", businessType = BusinessType.DELETE)
    @DeleteMapping()
    public R<Void> remove(@NotNull(message = "错题不能为空")
                          @RequestParam Long wrongQuestionRecordNewId) {
        return toAjax(recordService.deleteWithValidByIds(List.of(wrongQuestionRecordNewId), true));
    }

    @GetMapping("/subject/list")
    public R<List<RemoteSubjectVo>> listSubjects() {
        return R.ok(remoteCdsCommonService.listSubjects(null));
    }

    @GetMapping("/source/list")
    public R<List<RemoteDictDataVo>> listSource() {
        return R.ok(remoteDictService.selectDictDataByType("wrong_question_source"));
    }
}
