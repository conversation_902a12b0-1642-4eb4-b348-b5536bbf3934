package com.jxw.shufang.student.domain;


import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("wrong_question_record_new")
public class WrongQuestionRecordNew extends BaseEntity {
    @Serial
    private static final long serialVersionUID = 1L;
    /**
     * 错题id
     */
    @TableId(value = "wrong_question_record_new_id")
    private Long wrongQuestionRecordNewId;

    /**
     * 会员id
     */
    private Long studentId;

    /**
     * 题目ID
     */
    private Long questionId;

    /**
     * 科目ID（cds_subject）
     */
    private Integer subjectId;

    /**
     * 用户答案
     */
    private String userAnswer;

    /**
     * 用户答案状态（对应字典值，如全错 半错）
     */
    private String userAnswerStatus;

    /**
     * 来源类型(对应字典值)
     */
    private String sourceType;

    /**
     * 来源ID（学测练为courseId,AI评测为paperId）
     */
    private String sourceId;
}
