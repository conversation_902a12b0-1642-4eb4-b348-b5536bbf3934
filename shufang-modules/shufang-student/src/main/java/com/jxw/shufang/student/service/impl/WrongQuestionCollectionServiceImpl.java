package com.jxw.shufang.student.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jxw.shufang.common.core.utils.MapstructUtils;
import com.jxw.shufang.common.core.utils.StringUtils;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.web.core.BaseService;
import com.jxw.shufang.student.domain.WrongQuestionCollection;
import com.jxw.shufang.student.domain.bo.WrongQuestionCollectionBo;
import com.jxw.shufang.student.domain.vo.WrongQuestionCollectionVo;
import com.jxw.shufang.student.mapper.WrongQuestionCollectionMapper;
import com.jxw.shufang.student.service.IWrongQuestionCollectionService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 错题合集Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-07-30
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class WrongQuestionCollectionServiceImpl implements IWrongQuestionCollectionService, BaseService {

    private final WrongQuestionCollectionMapper baseMapper;

    /**
     * 查询错题合集
     */
    @Override
    public WrongQuestionCollectionVo queryById(Long wrongQuestionCollectionId) {
        return baseMapper.selectVoById(wrongQuestionCollectionId);
    }

    /**
     * 查询错题合集列表
     */
    @Override
    public TableDataInfo<WrongQuestionCollectionVo> queryPageList(WrongQuestionCollectionBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<WrongQuestionCollection> lqw = buildQueryWrapper(bo);
        Page<WrongQuestionCollectionVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询错题合集列表
     */
    @Override
    public List<WrongQuestionCollectionVo> queryList(WrongQuestionCollectionBo bo) {
        LambdaQueryWrapper<WrongQuestionCollection> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<WrongQuestionCollection> buildQueryWrapper(WrongQuestionCollectionBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<WrongQuestionCollection> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getStudentId() != null, WrongQuestionCollection::getStudentId, bo.getStudentId());
        lqw.eq(bo.getCollectionStatus() != null, WrongQuestionCollection::getCollectionStatus, bo.getCollectionStatus());
        lqw.eq(bo.getCollectionType() != null, WrongQuestionCollection::getCollectionType, bo.getCollectionType());
        lqw.like(StringUtils.isNotBlank(bo.getRemark()), WrongQuestionCollection::getRemark, bo.getRemark());
        return lqw;
    }

    /**
     * 新增错题合集
     */
    @Override
    public Boolean insertByBo(WrongQuestionCollectionBo bo) {
        WrongQuestionCollection add = BeanUtil.toBean(bo, WrongQuestionCollection.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setWrongQuestionCollectionId(add.getWrongQuestionCollectionId());
        }
        return flag;
    }

    /**
     * 修改错题合集
     */
    @Override
    public Boolean updateByBo(WrongQuestionCollectionBo bo) {
        WrongQuestionCollection update = BeanUtil.toBean(bo, WrongQuestionCollection.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(WrongQuestionCollection entity) {
        // TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除错题合集
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    /**
     * 根据学生ID查询错题合集列表
     */
    @Override
    public List<WrongQuestionCollectionVo> queryByStudentId(Long studentId) {
        LambdaQueryWrapper<WrongQuestionCollection> lqw = Wrappers.lambdaQuery();
        lqw.eq(WrongQuestionCollection::getStudentId, studentId);
        lqw.orderByDesc(WrongQuestionCollection::getCreateTime);
        return baseMapper.selectVoList(lqw);
    }

    /**
     * 根据学生ID和类型查询错题合集列表
     */
    @Override
    public List<WrongQuestionCollectionVo> queryByStudentIdAndType(Long studentId, Integer collectionType) {
        LambdaQueryWrapper<WrongQuestionCollection> lqw = Wrappers.lambdaQuery();
        lqw.eq(WrongQuestionCollection::getStudentId, studentId);
        lqw.eq(WrongQuestionCollection::getCollectionType, collectionType);
        lqw.orderByDesc(WrongQuestionCollection::getCreateTime);
        return baseMapper.selectVoList(lqw);
    }

    /**
     * 更新错题合集状态
     */
    @Override
    public Boolean updateCollectionStatus(Long wrongQuestionCollectionId, Integer collectionStatus) {
        WrongQuestionCollection update = new WrongQuestionCollection();
        update.setWrongQuestionCollectionId(wrongQuestionCollectionId);
        update.setCollectionStatus(collectionStatus);
        return baseMapper.updateById(update) > 0;
    }

}
